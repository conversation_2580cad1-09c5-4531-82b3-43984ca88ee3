<div class="priceScrollBox_price-scroll-container">
  <template v-if="parseFloat(price || 0) >= 10000">
    <div class="text-2xl" style="display: inline-flex; align-items: baseline">
      <template
        v-for="(digit, index) in formatPriceWithWanTag(price || 0).integerPart.split('')"
      >
        <span
          class="priceScrollBox_number-scroll"
          :key="'wan-' + index"
          :data-digit="digit"
        >
          <span class="priceScrollBox_number-scroll-inner">
            <span
              class="priceScrollBox_number-scroll-digit"
              v-for="num in [0,1,2,3,4,5,6,7,8,9]"
              :key="num"
              >{{num}}</span
            >
          </span>
        </span>
      </template>
    </div>
    <div class="priceScrollBox_wan-tag">
      <div class="wan-font">万</div>
      <div class="wan-triangle"></div>
    </div>
    <div class="text-2xl" style="display: inline-flex; align-items: baseline">
      <template
        v-for="(digit, index) in formatPriceWithWanTag(price || 0).remainderPart.split('')"
      >
        <span
          class="priceScrollBox_number-scroll"
          :key="'remainder-' + index"
          :data-digit="digit"
        >
          <span class="priceScrollBox_number-scroll-inner">
            <span
              class="priceScrollBox_number-scroll-digit"
              v-for="num in [0,1,2,3,4,5,6,7,8,9]"
              :key="num"
              >{{num}}</span
            >
          </span>
        </span>
      </template>
    </div>
    <span class="text-xl">.</span>
    <div class="text-xl" style="display: inline-flex; align-items: baseline">
      <template
        v-for="(digit, index) in formatPriceWithWanTag(price || 0).decimalPart.split('')"
      >
        <span
          class="priceScrollBox_number-scroll"
          :key="'decimal-' + index"
          :data-digit="digit"
        >
          <span class="priceScrollBox_number-scroll-inner">
            <span
              class="priceScrollBox_number-scroll-digit"
              v-for="num in [0,1,2,3,4,5,6,7,8,9]"
              :key="num"
              >{{num}}</span
            >
          </span>
        </span>
      </template>
    </div>
  </template>
  <template v-else>
    <div class="text-2xl" style="display: inline-flex; align-items: baseline">
      <template
        v-for="(digit, index) in (price || 0).toString().split('.')[0].split('')"
      >
        <span
          class="priceScrollBox_number-scroll"
          :key="'integer-' + index"
          :data-digit="digit"
        >
          <span class="priceScrollBox_number-scroll-inner">
            <span
              class="priceScrollBox_number-scroll-digit"
              v-for="num in [0,1,2,3,4,5,6,7,8,9]"
              :key="num"
              >{{num}}</span
            >
          </span>
        </span>
      </template>
    </div>
    <span class="text-xl">.</span>
    <div class="text-xl" style="display: inline-flex; align-items: baseline">
      <template
        v-for="(digit, index) in ((price || 0).toString().split('.')[1] || '00').split('')"
      >
        <span
          class="priceScrollBox_number-scroll"
          :key="'decimal-' + index"
          :data-digit="digit"
        >
          <span class="priceScrollBox_number-scroll-inner">
            <span
              class="priceScrollBox_number-scroll-digit"
              v-for="num in [0,1,2,3,4,5,6,7,8,9]"
              :key="num"
              >{{num}}</span
            >
          </span>
        </span>
      </template>
    </div>
  </template>
</div>
