/* 万位标签样式 */
.priceScrollBox_wan-tag {
  display: inline-flex;
  position: relative;
  align-items: baseline;
  vertical-align: baseline; /* 确保与数字基线对齐 */
  width: 2px;
  height: 1em; /* 使用相对单位确保与数字高度一致 */
}
.priceScrollBox_wan-tag > .wan-font {
  position: absolute;
  top: 0;
  left: 0;
  transform: translateX(-4px) translateY(-18px);
  color: rgb(107, 114, 128);
  font-size: 12px;
  line-height: 12px;
  white-space: nowrap;
}
.priceScrollBox_wan-tag > .wan-triangle {
  position: absolute;
  top: 0;
  left: 0;
  transform: translateX(-3px) translateY(-4px);
  border-top: 4px solid #dc2626;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  width: 0;
  height: 0;
  content: "";
  line-height: 12px;
}

/* 数字滚动动画样式 */
.priceScrollBox_number-scroll {
  display: inline-block;
  position: relative;
  vertical-align: baseline;
  width: 0.6em;
  height: 1em;
  overflow: hidden;
}

.priceScrollBox_number-scroll-inner {
  display: flex;
  flex-direction: column;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  height: 1000%; /* 10个数字，每个占100% */
}

.priceScrollBox_number-scroll-digit {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 10%; /* 每个数字占总高度的10% */
  font-weight: inherit;
  font-size: inherit;
  line-height: 1;
}

/* 数字滚动容器 */
.priceScrollBox_price-scroll-container {
  display: inline-flex;
  align-items: baseline;
}

/* 确保数字在不同字体大小下正确对齐 */
.text-2xl .priceScrollBox_number-scroll {
  width: 0.6em;
  height: 1em;
}

.text-xl .priceScrollBox_number-scroll {
  width: 0.6em;
  height: 1em;
}
